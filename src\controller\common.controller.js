const { sql, queryWithParams } = require('../service/index.js')
const jwt = require('jsonwebtoken')

class CommonController {
  async verify(ctx, next) {
    try {
      const authorization = ctx.headers.authorization
      if (!authorization) return (ctx.body = { code: 200, data: false, msg: '验证失败' })
      const token = authorization.replace('Bearer ', '')
      // 解析token
      const result = jwt.verify(token, process.env.JWT_SECRET, { algorithms: ['HS256'] })
      const id = result['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier']
      const userName = result['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name']
      const { recordsets } = await queryWithParams(`SELECT * FROM dbo.[User] WHERE id = @id AND Username = @userName`, {
        id: { type: sql.Int, value: id },
        userName: { type: sql.NVarChar, value: userName }
      })
      if (!recordsets[0][0].id) return (ctx.body = { code: 200, data: false, msg: '验证失败' })
      ctx.body = { code: 200, data: true, msg: '查询成功' }
    } catch (error) {
      ctx.body = { code: 200, data: false, msg: '验证失败', error: error.message }
    }
  }
}

module.exports = new CommonController()
