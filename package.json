{"name": "nodeserver", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node --watch index.js"}, "author": "", "license": "ISC", "dependencies": {"@alicloud/sts-sdk": "^1.0.2", "@koa/cors": "^5.0.0", "@koa/router": "^13.0.0", "ali-oss": "^6.22.0", "axios": "^1.10.0", "dotenv": "^16.4.7", "eviltransform": "^0.2.2", "exceljs": "^4.4.0", "jsonwebtoken": "^9.0.2", "koa": "^2.15.3", "koa-body": "^6.0.1", "koa-static": "^5.0.0", "mammoth": "^1.8.0", "mime-types": "^2.1.35", "mssql": "^11.0.1", "pdf-parse": "^1.1.1"}}