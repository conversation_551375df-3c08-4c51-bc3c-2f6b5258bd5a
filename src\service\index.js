// src/service/index.js
const sql = require('mssql')
const dotenv = require('dotenv')

dotenv.config()
const { MSSQL_USERNAME, MSSQL_PASSWORD, MSSQL_SERVER, MSSQL_DATABASE } = process.env

const config = {
  user: MSSQL_USERNAME,
  password: MSSQL_PASSWORD,
  server: MSSQL_SERVER,
  database: MSSQL_DATABASE,
  port: 1433,
  options: {
    encrypt: true,
    trustServerCertificate: true
  }
}

const pool = new sql.ConnectionPool(config)
const poolConnect = pool.connect()

pool.on('error', (err) => {
  console.error('数据库连接池错误:', err)
})

/**
 * 执行参数化 SQL 查询
 * @param {string} text - SQL 语句
 * @param {Object} [params] - 参数对象，如 { name: { type: sql.VarChar, value: 'xxx' } }
 * @returns {Promise<any>}
 */
async function queryWithParams(text, params = {}) {
  await poolConnect
  const request = pool.request()
  for (const key in params) {
    const { type, value } = params[key]
    request.input(key, type, value)
  }
  return request.query(text)
}

module.exports = {
  query: async (text) => {
    await poolConnect
    return pool.request().query(text)
  },
  queryWithParams,
  sql, // 导出 mssql 类型
  getPool: async () => {
    await poolConnect
    return pool
  },
  close: async () => {
    await pool.close()
  }
}
