const fs = require('fs')
const path = require('path')
const sql = require('../service/index.js')
const fileParh = path.join(__dirname, '../assets/data/neighbour.statistics.json')
class Statistics {
  async gain(ctx, next) {
    //读取文件
    try {
      const data = await fs.promises.readFile(fileParh, 'utf8')
      ctx.body = { code: 200, message: '查询成功', data: JSON.parse(data) }
    } catch (error) {
      ctx.body = { code: 500, message: '查询失败', data: error.message }
    }
  }
  async update(ctx, next) {
    try {
      const data = ctx.request.body
      fs.promises.writeFile(fileParh, JSON.stringify(data))
      ctx.body = { code: 200, message: '更新成功', data }
    } catch (error) {
      ctx.body = { code: 500, message: '更新失败', data: error.message }
    }
  }

  async queryCount(ctx, next) {
    try {
      const { tier, type, screen } = ctx.request.body
      const tiers = { 网格: 'gridding', 片区: 'BelongingArea', 街道: 'BelongingStreet' }
      const types = { Type: '1, 2, 3, 4, 5', classify: "'未达标', '已达标', '未改造', '已改造', '正在改造'", OperationManagementState: "'集团自管', '集团自管（移交水务科技）', '施工单位管理', '小区物业管理'" }

      const sentence = `SELECT ${type} AS name, COUNT(*) AS count  FROM SecondaryWaterProgress  WHERE ${screen ? `${tiers[tier]} = '${screen}' AND` : ''} ${type} IN (${types[type]})  GROUP BY ${type};`
      console.log(sentence)
      const res = await sql.query(sentence)
      ctx.body = { code: 200, message: '查询成功', data: res }
    } catch (error) {
      ctx.body = { code: 500, message: '查询失败', data: error.message }
    }
  }
}

module.exports = new Statistics()
