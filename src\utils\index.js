// 版本对比
/**
 * 版本号对比 a>b 返回true 否则返回false
 * @param {String} a
 * @param {String} b
 * @returns {Boolean}
 */
function compare(a, b) {
  const _a = a.split('.').map(Number)
  const _b = b.split('.').map(Number)
  for (let i = 0; i < _a.length; i++) {
    if (_a[i] > _b[i]) return true
    if (_a[i] < _b[i]) return false
  }
  return false
}

function isNumber(value) {
  return typeof value === 'number' && !isNaN(value)
}
module.exports = { compare, isNumber }
