const { sql, query, queryWithParams } = require('../service/index.js')

class UptownMaterialController {
  async gain(ctx) {
    const { code } = ctx.request.query
    try {
      const sentence = `SELECT * FROM dbo.[dwd_gwyy_xqxttz] WHERE xqbm = @code`
      const { recordsets } = await queryWithParams(sentence, { code: { type: sql.VarChar, value: code } })
      ctx.body = { code: 200, data: recordsets[0][0] }
    } catch (error) {
      ctx.body = { code: 500, error: error.message }
    }
  }
  async list(ctx) {
    try {
      const { xqmc, sws, exist, page = 1, pageSize = 15 } = ctx.request.query
      const pageNum = parseInt(page)
      const pageSizeNum = parseInt(pageSize)
      const offset = (pageNum - 1) * pageSizeNum

      const field = `xqbm, xqmc, is_pump_room, sws, xqlb, ssjd`
      const where = `${
        xqmc
          ? 'WHERE xqmc LIKE @xqmc'
          : sws && exist
          ? `WHERE sws LIKE @sws AND is_pump_room  ${exist == '是' ? '' : 'NOT'} LIKE @exist`
          : sws
          ? 'WHERE sws LIKE @sws'
          : exist
          ? `WHERE is_pump_room ${exist == '是' ? '' : 'NOT'} LIKE @exist`
          : ''
      }`

      // 获取总数的查询
      const countSentence = `SELECT COUNT(*) as total FROM dbo.[dwd_gwyy_xqxttz] ${where}`
      const { recordsets: countResult } = await queryWithParams(countSentence, {
        sws: { type: sql.VarChar, value: `%${sws}%` },
        exist: { type: sql.VarChar, value: `%是%` },
        xqmc: { type: sql.VarChar, value: `%${xqmc}%` }
      })
      const total = countResult[0][0].total

      const sentence = `SELECT ${field} FROM dbo.[dwd_gwyy_xqxttz] ${where} ORDER BY xqbm OFFSET @offset ROWS FETCH NEXT @pageSize ROWS ONLY`

      const { recordsets } = await queryWithParams(sentence, {
        sws: { type: sql.VarChar, value: `%${sws}%` },
        exist: { type: sql.VarChar, value: `%是%` },
        xqmc: { type: sql.VarChar, value: `%${xqmc}%` },
        offset: { type: sql.Int, value: offset },
        pageSize: { type: sql.Int, value: pageSizeNum }
      })

      // 计算分页信息
      const totalPages = Math.ceil(total / pageSizeNum)

      ctx.body = {
        code: 200,
        msg: '查询成功',
        data: recordsets[0],
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total,
          totalPages,
          hasNext: pageNum < totalPages,
          hasPrev: pageNum > 1
        }
      }
    } catch (error) {
      ctx.body = { code: 500, error: error.message }
    }
  }

  async codes(ctx) {
    try {
      const sentence = `SELECT Zone_Code FROM [dbo].[DIST_ADDRESS]  where Client_Name is not NULL`
      const { recordsets } = await queryWithParams(sentence, {})
      const list = [...new Set(recordsets[0].map((item) => item.Zone_Code))]
      ctx.body = { code: 200, msg: '查询成功', data: { total: list.length, list } }
    } catch (error) {
      ctx.body = { code: 500, error: error.message }
    }
  }
}

module.exports = new UptownMaterialController()
