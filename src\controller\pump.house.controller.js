const sql = require('../service/index.js')
const { isNumber } = require('../utils/index.js')
const { buildUUID, buildShortUUID } = require('../utils/uuid.js')

class PumpHouseController {
  async create(ctx, next) {
    try {
      const bodyData = ctx.request.body
      bodyData.PumpRoomNumber = buildUUID()
      bodyData.PumpRoomID = buildShortUUID('PumpRoomID')
      bodyData.SYSTEMID = buildShortUUID('SYSTEMID')
      bodyData.CurrentNode = 1

      const keys = Object.keys(bodyData).join(',')
      const values = Object.values(bodyData)
        .map((item) => {
          if (item === null) return 'NULL'
          if (isNumber(item)) return item
          return `'${item}'`
        })
        .join(',')
      const sentence = `INSERT INTO dbo.[SecondaryWaterProgressNew] (${keys}) VALUES (${values});`
      await sql.query(sentence)
      const { recordsets } = await sql.query(`SELECT * FROM dbo.[SecondaryWaterProgressNew] WHERE PumpRoomNumber = '${bodyData.PumpRoomNumber}'`)
      ctx.body = { code: 200, msg: '创建成功', data: recordsets[0][0] }
    } catch (error) {
      ctx.body = { code: 400, msg: '创建失败', data: error.message }
    }
  }
  async update(ctx, next) {
    try {
      const bodyData = ctx.request.body
      const PumpRoomNumber = bodyData.PumpRoomNumber
      delete bodyData.PumpRoomNumber
      delete bodyData.Id
      delete bodyData.CurrentNode
      delete bodyData.UpdateTime
      bodyData.UpdatePerson = ctx.user.Name
      const data = Object.entries(bodyData)
      const sentence = `UPDATE SecondaryWaterProgressNew SET ${data.map((item) => `${item[0]} = ${item[1] === null || isNumber(item[1]) ? item[1] : `'${item[1]}'`}`).join(', ')} WHERE PumpRoomNumber = '${PumpRoomNumber}';`
      sql.query(sentence)
      ctx.body = { code: 200, message: '修改成功' }
    } catch (error) {
      ctx.body = { code: 400, message: '修改失败', data: error.message }
    }
  }
  async gain(ctx, next) {
    try {
      const { PumpRoomNumber } = ctx.params
      const sentence = `SELECT * FROM SecondaryWaterProgressNew WHERE PumpRoomNumber = '${PumpRoomNumber}'`
      const { recordsets } = await sql.query(sentence)
      ctx.body = { code: 200, message: '查询成功', data: recordsets[0][0] }
    } catch (error) {
      ctx.body = { code: 400, message: '查询失败', data: error.message }
    }
  }
  async list(ctx, next) {
    try {
      const { startTime, endTime, all } = ctx.query
      let sentence = `SELECT  PumpRoomNumber,
                              BelongingArea,
                              PumpHouseName,
                              RemouldState,
                              Gridding,
                              BelongingStreet,
                              PumpRoomControlledState,
                              AccuratePosition,
                              OperationManagementState,
                              ProgressStatus,
                              X,
                              Y,
                              ZoneCode,
                              CurrentNode,
                              UpdatePerson,
                              UpdateTime,
                              Batch FROM SecondaryWaterProgressNew`
      if (all) {
        sentence = `SELECT * FROM SecondaryWaterProgressNew`
      }
      if (startTime && endTime) {
        if (all) {
          sentence = `SELECT * FROM SecondaryWaterProgressNew WHERE UpdateTime >= '${startTime}' AND UpdateTime <='${endTime}'`
        } else {
          sentence = `SELECT  PumpRoomNumber, 
                              BelongingArea,
                              PumpHouseName,
                              RemouldState,
                              Gridding,
                              BelongingStreet,
                              PumpRoomControlledState,
                              AccuratePosition,
                              OperationManagementState,
                              ProgressStatus,
                              X,
                              Y,
                              ZoneCode,
                              CurrentNode,
                              UpdatePerson,
                              UpdateTime,
                              Batch FROM SecondaryWaterProgressNew WHERE UpdateTime >= '${startTime}' AND UpdateTime <='${endTime}'`
        }
      }
      const { recordsets } = await sql.query(sentence)
      ctx.body = { code: 200, message: '查询成功', data: recordsets[0] }
    } catch (error) {
      ctx.body = { code: 400, message: '查询失败12', data: error.message }
    }
  }

  async seek(ctx, next) {
    try {
      const { pumpHouseName } = ctx.query
      const sentence = `SELECT * FROM SecondaryWaterProgressNew WHERE PumpHouseName LIKE '%${pumpHouseName}%'`
      const { recordsets } = await sql.query(sentence)
      ctx.body = { code: 200, message: '查询成功', data: recordsets[0] }
    } catch (error) {
      ctx.body = { code: 400, message: '查询失败', data: error.message }
    }
  }
  async seekScope(ctx, next) {
    try {
      const { ZoneCode } = ctx.query
      const sentence = `SELECT * FROM SecondaryWaterProgressNew WHERE ZoneCode = '${ZoneCode}' `
      const { recordsets } = await sql.query(sentence)
      ctx.body = { code: 200, message: '查询成功', data: recordsets[0] }
    } catch (error) {
      ctx.body = { code: 400, message: '查询失败', data: error.message }
    }
  }
}
class PumpHouseNodeController {
  async create(ctx, next) {
    try {
      const { PumpRoomNumber, Node, CompletionTime } = ctx.request.body
      if (!PumpRoomNumber || !Node) return (ctx.body = { code: 400, msg: '参数缺失' })
      const { recordsets } = await sql.query(`SELECT * FROM SecondaryWaterProgressNode WHERE Node = ${Node} AND PumpRoomNumber = '${PumpRoomNumber}'; `)
      if (recordsets[0].length) return (ctx.body = { code: 200, msg: '节点已存在' })
      const sentence = `INSERT INTO dbo.[SecondaryWaterProgressNode] (PumpRoomNumber,IsEnd,Node, CompletionTime, Remark, UpdatePerson) 
      VALUES ('${PumpRoomNumber}',0,'${Node}','${CompletionTime}',NULL,'${ctx.user.Name}')`
      await sql.query(sentence)

      const { recordsets: res } = await sql.query(`SELECT MAX(Node) AS newestNode  FROM SecondaryWaterProgressNode WHERE PumpRoomNumber = '${PumpRoomNumber}';`)
      if (res[0][0].newestNode) {
        sql.query(`UPDATE dbo.[SecondaryWaterProgressNew] SET CurrentNode = ${res[0][0].newestNode} WHERE  PumpRoomNumber = '${PumpRoomNumber}';`)
      } else {
        sql.query(`UPDATE dbo.[SecondaryWaterProgressNew] SET CurrentNode = '1' WHERE  PumpRoomNumber = '${PumpRoomNumber}';`)
      }
      ctx.body = { code: 200, message: '添加成功' }
    } catch (error) {
      ctx.body = { code: 400, message: '添加失败', data: error.message }
    }
  }
  async update(ctx, next) {
    try {
      const { PumpRoomNumber, IsEnd, Node, CompletionTime, Remark } = ctx.request.body
      if (!PumpRoomNumber || !Node || !CompletionTime) return (ctx.body = { code: 400, msg: '参数缺失' })
      const { recordsets } = await sql.query(`SELECT * FROM SecondaryWaterProgressNode WHERE Node = ${Node} AND PumpRoomNumber = '${PumpRoomNumber}'; `)
      if (recordsets[0].length) {
        const sentence = `UPDATE dbo.[SecondaryWaterProgressNode] SET IsEnd = '${IsEnd}', CompletionTime = '${CompletionTime}', Remark = '${Remark}', UpdatePerson = '${ctx.user.Name}' WHERE Node = ${Node} AND PumpRoomNumber = '${PumpRoomNumber}'; `
        await sql.query(sentence)
      } else {
        const sentence = `INSERT INTO dbo.[SecondaryWaterProgressNode] (PumpRoomNumber,IsEnd,Node, CompletionTime, Remark, UpdatePerson)
        VALUES ('${PumpRoomNumber}','${IsEnd}','${Node}','${CompletionTime}',${Remark ? `'${Remark}'` : 'NULL'},'${ctx.user.Name}')`
        await sql.query(sentence)
      }

      const { recordsets: res } = await sql.query(`SELECT MAX(Node) AS newestNode  FROM SecondaryWaterProgressNode WHERE PumpRoomNumber = '${PumpRoomNumber}' AND IsEnd = 1;`)
      if (res[0][0].newestNode) {
        sql.query(`UPDATE dbo.[SecondaryWaterProgressNew] SET CurrentNode = ${res[0][0].newestNode}, UpdatePerson = '${ctx.user.Name}' WHERE  PumpRoomNumber = '${PumpRoomNumber}';`)
      } else {
        sql.query(`UPDATE dbo.[SecondaryWaterProgressNew] SET CurrentNode = '1' WHERE  PumpRoomNumber = '${PumpRoomNumber}';`)
      }

      ctx.body = { code: 200, message: '更新成功' }
    } catch (error) {
      ctx.body = { code: 400, message: '更新失败', data: error.message }
    }
  }
  async gain(ctx, next) {
    try {
      const { PumpRoomNumber } = ctx.params
      const { Node } = ctx.query
      const sentence = `SELECT 
        n.*,
        (SELECT * FROM SecondaryWaterProgressNodeFiles AS f WHERE f.PumpRoomNumber = n.PumpRoomNumber AND f.Node = n.Node FOR JSON PATH) AS Files
        FROM SecondaryWaterProgressNode AS n WHERE n.PumpRoomNumber = '${PumpRoomNumber}' AND n.Node = ${Node};`
      const { recordsets } = await sql.query(sentence)
      ctx.body = { code: 200, message: '获取成功', data: recordsets[0][0] ?? null }
    } catch (error) {
      ctx.body = { code: 400, message: '获取失败', data: error.message }
    }
  }
  async list(ctx, next) {
    try {
      const { PumpRoomNumber } = ctx.params
      const sentence = `SELECT 
        n.*,
        (SELECT * FROM SecondaryWaterProgressNodeFiles AS f WHERE f.PumpRoomNumber = n.PumpRoomNumber AND f.Node = n.Node FOR JSON PATH) AS Files
        FROM SecondaryWaterProgressNode AS n WHERE n.PumpRoomNumber = '${PumpRoomNumber}' ORDER BY Node ASC;`
      const { recordsets } = await sql.query(sentence)
      ctx.body = { code: 200, message: '获取成功', data: recordsets[0] }
    } catch (error) {
      ctx.body = { code: 400, message: '获取失败', data: error.message }
    }
  }
}
class PumpHouseNodeFileController {
  async create(ctx, next) {
    try {
      const { PumpRoomNumber, Node, FileType, Path } = ctx.request.body
      if (!PumpRoomNumber || !Node || !FileType || !Path) return (ctx.body = { code: 400, msg: '参数缺失' })
      const sentence = `INSERT INTO SecondaryWaterProgressNodeFiles (PumpRoomNumber, Node, FileType, Path, UploadPerson) VALUES ('${PumpRoomNumber}', '${Node}', '${FileType}', '${Path}','${ctx.user.Name}')`
      await sql.query(sentence)
      ctx.body = { code: 200, message: '添加成功' }
    } catch (error) {
      ctx.body = { code: 400, message: '添加失败', data: error.message }
    }
  }
  async update(ctx, next) {
    try {
      const { PumpRoomNumber, Node, FileType, Path } = ctx.request.body
      if (!PumpRoomNumber || !Node || !FileType || !Path) return (ctx.body = { code: 400, msg: '参数缺失' })
      const { recordsets } = await sql.query(`SELECT * FROM SecondaryWaterProgressNodeFiles WHERE PumpRoomNumber = '${PumpRoomNumber}' AND Node = '${Node}' AND FileType = '${FileType}'`)
      if (!recordsets[0].length) return (ctx.body = { code: 400, msg: '未找到该条记录' })
      const sentence = `UPDATE SecondaryWaterProgressNodeFiles SET Path = '${Path}',UploadPerson = ${ctx.user.Name} WHERE PumpRoomNumber = '${PumpRoomNumber}' AND Node = '${Node}' AND FileType = '${FileType}'`
      await sql.query(sentence)
    } catch (error) {
      ctx.body = { code: 400, message: '修改失败', data: error.message }
    }
  }
}

const pumpHouse = new PumpHouseController()
const node = new PumpHouseNodeController()
const nodeFile = new PumpHouseNodeFileController()

module.exports = { pumpHouse, node, nodeFile }
